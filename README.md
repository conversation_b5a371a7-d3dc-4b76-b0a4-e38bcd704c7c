# Project Chimera 🐍

**Automated Token Unlock Arbitrage Trading System**

Project Chimera is a sophisticated microservices-based trading system that automatically identifies and executes profitable short-selling opportunities around token unlock events. The system leverages predictable market behavior where token prices typically decline before large unlock events due to anticipated selling pressure.

## 🎯 Strategy Overview

The core strategy exploits the "unlock decay" phenomenon:
1. **Identify** upcoming token unlocks that will significantly impact circulating supply
2. **Analyze** the potential price impact using our proprietary Unlock Pressure Score
3. **Execute** short positions on borrowable tokens before the unlock event
4. **Manage** risk with automated stop-loss and take-profit mechanisms
5. **Close** positions before the actual unlock to capture the decay premium

## 🏗️ Architecture

Project Chimera follows a microservices architecture with 5 core services:

### 🔮 The Oracle (Data Ingestion)
- **Purpose**: Fetches token unlock data from external APIs
- **Schedule**: Daily cron job at 1:00 AM UTC
- **Sources**: TokenUnlocks.com, Vestlab.io APIs
- **Output**: Publishes unlock events to Redis

### 🧠 The Seer (Strategy Engine)
- **Purpose**: Analyzes unlock events and identifies trade candidates
- **Algorithm**: Calculates Unlock Pressure Score using supply/volume metrics
- **Filters**: Only considers tokens borrowable on Aave/Compound
- **Output**: Publishes high-conviction trade candidates

### ⚔️ The Executioner (Trade Execution)
- **Purpose**: Executes short-selling trades on-chain
- **Process**: Borrows tokens → Swaps to USDC → Logs position
- **Protocols**: Aave V3 for lending, 1inch for DEX aggregation
- **Security**: Private keys stored as encrypted secret files

### 📊 The Ledger (Risk Management)
- **Purpose**: Monitors open positions and enforces risk rules
- **Monitoring**: Real-time price tracking every 60 seconds
- **Rules**: 15% stop-loss, 10% take-profit, time-based exits
- **Output**: Publishes close position events when triggered

### 📢 The Herald (Notifications)
- **Purpose**: Sends formatted alerts to Telegram
- **Channels**: Monitors all system events via Redis patterns
- **Format**: Rich markdown notifications with emojis and metrics
- **Reliability**: Error handling and message splitting for long content

## 🚀 Quick Start

### Prerequisites
- Python 3.10+
- PostgreSQL database
- Redis instance
- Ethereum wallet with ETH for gas
- API keys for data sources
- Telegram bot for notifications

### 1. Clone and Setup
```bash
git clone <your-repo-url>
cd project-chimera
```

### 2. Environment Configuration
Create environment variables in your deployment platform:

```bash
# Database
DATABASE_URL=postgresql://user:pass@host:port/db
REDIS_URL=redis://user:pass@host:port

# Blockchain
INFURA_API_KEY=your_infura_key
PRIVATE_KEY_PATH=/etc/secrets/trader-pk

# Data Sources
TOKENUNLOCKS_API_KEY=your_tokenunlocks_key
VESTLAB_API_KEY=your_vestlab_key

# Notifications
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id

# Risk Parameters (optional)
PRESSURE_SCORE_THRESHOLD=0.75
STOP_LOSS_PCT=0.15
TAKE_PROFIT_PCT=0.10
BORROW_AMOUNT_PER_TRADE=1000
```

### 3. Database Initialization
```bash
cd database
python init_db.py init
```

### 4. Deploy to Render.com
1. Fork this repository
2. Update the `repo` URLs in `render.yaml`
3. Connect your GitHub repo to Render
4. Deploy using the Blueprint feature with `render.yaml`
5. Set environment variables in Render dashboard
6. Upload your private key as a Secret File

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Install test dependencies
pip install pytest pytest-cov

# Run all tests using the test runner
python scripts/run_tests.py --all

# Run specific test categories
python scripts/run_tests.py --unit
python scripts/run_tests.py --integration
python scripts/run_tests.py --coverage

# Run individual test files
python -m pytest tests/test_oracle.py -v
python -m pytest tests/test_seer.py -v
python -m pytest tests/test_ledger.py -v
python -m pytest tests/test_integration.py -v

# Run with coverage
python -m pytest tests/ --cov=services --cov-report=html
```

## 📊 Monitoring & Operations

### System Health Checks
- **Database**: `python database/init_db.py check`
- **Redis**: Check service logs for connection status
- **Telegram**: Send test notification via Herald
- **Blockchain**: Monitor gas prices and wallet balance
- **Unified Config**: All services use centralized configuration
- **L2 Networks**: Optimized for Arbitrum/Polygon deployment

### Key Metrics to Monitor
- **Unlock Events**: Daily ingestion count
- **Trade Candidates**: Pressure scores and filtering rate
- **Positions**: Open count, P&L, time to unlock
- **Risk Alerts**: Stop-loss triggers, take-profit executions
- **System Events**: Error rates, processing times

### Operational Procedures
1. **Daily Review**: Check Oracle ingestion and new candidates
2. **Position Monitoring**: Review open positions and risk metrics
3. **Performance Analysis**: Track strategy performance and adjust parameters
4. **Risk Management**: Monitor exposure and adjust position sizes

## 🔧 Configuration

### Risk Parameters
- `PRESSURE_SCORE_THRESHOLD`: Minimum score for trade execution (default: 0.75)
- `STOP_LOSS_PCT`: Maximum loss before closing position (default: 15%)
- `TAKE_PROFIT_PCT`: Profit target for early exit (default: 10%)
- `TAKE_PROFIT_DAYS_BEFORE_UNLOCK`: Days before unlock to force exit (default: 1)
- `BORROW_AMOUNT_PER_TRADE`: Fixed amount to borrow per trade (default: 1000)

### Monitoring Settings
- `MONITORING_INTERVAL_SECONDS`: Price check frequency (default: 60)

## 🛡️ Security Considerations

### Private Key Management
- **Never** commit private keys to version control
- Use Render's Secret Files feature for production
- Rotate keys regularly and monitor wallet activity

### API Security
- Store all API keys as environment variables
- Use read-only API keys where possible
- Monitor API usage and rate limits

### Smart Contract Risks
- Test all transactions on testnets first
- Monitor gas prices and set reasonable limits
- Implement circuit breakers for unusual market conditions

## 📈 Performance Optimization

### Cost Optimization
- Uses free tiers: Render (750 hours), PostgreSQL (1GB), Redis (25MB)
- Efficient API usage with caching and rate limiting
- Optimized database queries with proper indexing

### Scalability
- Microservices can be scaled independently
- Redis pub/sub enables loose coupling
- Database designed for high-frequency updates

## 🐛 Troubleshooting

### Common Issues
1. **Database Connection**: Check DATABASE_URL format and network access
2. **Redis Pub/Sub**: Verify REDIS_URL and service connectivity
3. **API Rate Limits**: Implement exponential backoff and caching
4. **Gas Estimation**: Monitor network congestion and adjust gas prices
5. **Telegram Notifications**: Verify bot token and chat ID

### Debug Mode
Set environment variables for verbose logging:
```bash
PYTHONPATH=.
LOGGING_LEVEL=DEBUG
```

## 📚 Additional Resources

- [Aave V3 Documentation](https://docs.aave.com/developers/)
- [1inch API Documentation](https://docs.1inch.io/)
- [Render Deployment Guide](https://render.com/docs)
- [Redis Pub/Sub Guide](https://redis.io/topics/pubsub)

## ⚖️ Legal Disclaimer

This software is for educational and research purposes only. Cryptocurrency trading involves substantial risk of loss. Users are responsible for:
- Compliance with local regulations
- Understanding smart contract risks
- Managing their own funds and private keys
- Conducting proper due diligence

The authors assume no responsibility for financial losses or legal issues arising from the use of this software.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add comprehensive tests
4. Update documentation
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

---

**Built with ❤️ for the DeFi community**
