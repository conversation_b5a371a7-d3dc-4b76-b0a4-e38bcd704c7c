#!/usr/bin/env python3
"""
Parameter Sensitivity Analysis for Project Chimera
==================================================

Tests strategy performance across different parameter combinations
to identify curve-fitting and ensure robustness.

If the strategy only works with one exact set of parameters,
it's curve-fitted garbage that will fail in live markets.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from itertools import product
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

from rigorous_backtest import RigorousBacktester, BacktestConfig, BacktestResults

class ParameterSensitivityAnalyzer:
    """
    Analyze strategy sensitivity to parameter changes
    """
    
    def __init__(self):
        self.results_matrix = []
        
    def run_sensitivity_analysis(self) -> pd.DataFrame:
        """
        Run backtest across parameter grid
        """
        print("🔬 PARAMETER SENSITIVITY ANALYSIS")
        print("=" * 40)
        
        # Parameter ranges to test
        pressure_thresholds = [0.60, 0.65, 0.70, 0.75, 0.80, 0.85, 0.90]
        stop_losses = [0.08, 0.10, 0.12, 0.15, 0.18, 0.20]
        take_profits = [0.20, 0.25, 0.30, 0.35, 0.40]
        
        total_combinations = len(pressure_thresholds) * len(stop_losses) * len(take_profits)
        print(f"Testing {total_combinations} parameter combinations...")
        
        results = []
        combination_count = 0
        
        for pressure_threshold, stop_loss, take_profit in product(
            pressure_thresholds, stop_losses, take_profits
        ):
            combination_count += 1
            
            # Skip combinations with poor risk/reward
            risk_reward_ratio = take_profit / stop_loss
            if risk_reward_ratio < 1.5:
                continue
                
            print(f"Testing combination {combination_count}/{total_combinations}: "
                  f"Pressure={pressure_threshold}, Stop={stop_loss:.0%}, "
                  f"Target={take_profit:.0%}, R:R={risk_reward_ratio:.1f}")
            
            try:
                # Create config for this combination
                config = BacktestConfig(
                    pressure_score_threshold=pressure_threshold,
                    stop_loss_pct=stop_loss,
                    take_profit_pct=take_profit
                )
                
                # Run backtest
                backtester = RigorousBacktester(config)
                backtest_results = backtester.run_backtest()
                
                # Store results
                result_row = {
                    'pressure_threshold': pressure_threshold,
                    'stop_loss_pct': stop_loss,
                    'take_profit_pct': take_profit,
                    'risk_reward_ratio': risk_reward_ratio,
                    'total_trades': len(backtest_results.trades),
                    'win_rate': backtest_results.win_rate,
                    'total_return': backtest_results.total_return,
                    'annualized_return': backtest_results.annualized_return,
                    'sharpe_ratio': backtest_results.sharpe_ratio,
                    'sortino_ratio': backtest_results.sortino_ratio,
                    'max_drawdown': backtest_results.max_drawdown,
                    'avg_risk_reward': backtest_results.avg_risk_reward,
                    'profit_factor': self._calculate_profit_factor(backtest_results.trades)
                }
                
                results.append(result_row)
                
            except Exception as e:
                print(f"  ❌ Failed: {e}")
                continue
        
        results_df = pd.DataFrame(results)
        
        if results_df.empty:
            raise ValueError("No valid parameter combinations found")
            
        return results_df
    
    def _calculate_profit_factor(self, trades) -> float:
        """Calculate profit factor (gross profit / gross loss)"""
        winning_trades = [t for t in trades if t.pnl_net > 0]
        losing_trades = [t for t in trades if t.pnl_net < 0]
        
        gross_profit = sum(t.pnl_net for t in winning_trades)
        gross_loss = abs(sum(t.pnl_net for t in losing_trades))
        
        return gross_profit / gross_loss if gross_loss > 0 else 0
    
    def analyze_results(self, results_df: pd.DataFrame) -> Dict:
        """
        Analyze sensitivity results for robustness
        """
        print("\n📊 SENSITIVITY ANALYSIS RESULTS")
        print("=" * 35)
        
        # Filter for profitable strategies only
        profitable_strategies = results_df[results_df['total_return'] > 0]
        
        if profitable_strategies.empty:
            print("🚨 CRITICAL: NO PROFITABLE PARAMETER COMBINATIONS FOUND")
            print("This strategy is fundamentally flawed.")
            return {'status': 'FAILED', 'profitable_count': 0}
        
        print(f"✅ Profitable combinations: {len(profitable_strategies)}/{len(results_df)}")
        print(f"   Success rate: {len(profitable_strategies)/len(results_df):.1%}")
        
        # Best performing combination
        best_strategy = profitable_strategies.loc[profitable_strategies['sharpe_ratio'].idxmax()]
        print(f"\n🏆 BEST COMBINATION:")
        print(f"   Pressure Threshold: {best_strategy['pressure_threshold']}")
        print(f"   Stop Loss: {best_strategy['stop_loss_pct']:.1%}")
        print(f"   Take Profit: {best_strategy['take_profit_pct']:.1%}")
        print(f"   Sharpe Ratio: {best_strategy['sharpe_ratio']:.2f}")
        print(f"   Total Return: {best_strategy['total_return']:.1%}")
        print(f"   Max Drawdown: {best_strategy['max_drawdown']:.1%}")
        
        # Robustness analysis
        print(f"\n🔍 ROBUSTNESS ANALYSIS:")
        
        # Check parameter stability
        pressure_stability = self._analyze_parameter_stability(
            profitable_strategies, 'pressure_threshold', 'sharpe_ratio'
        )
        stop_loss_stability = self._analyze_parameter_stability(
            profitable_strategies, 'stop_loss_pct', 'sharpe_ratio'
        )
        take_profit_stability = self._analyze_parameter_stability(
            profitable_strategies, 'take_profit_pct', 'sharpe_ratio'
        )
        
        print(f"   Pressure Threshold Stability: {pressure_stability:.2f}")
        print(f"   Stop Loss Stability: {stop_loss_stability:.2f}")
        print(f"   Take Profit Stability: {take_profit_stability:.2f}")
        
        # Overall robustness score
        robustness_score = (pressure_stability + stop_loss_stability + take_profit_stability) / 3
        print(f"   Overall Robustness Score: {robustness_score:.2f}")
        
        if robustness_score < 0.3:
            print("🚨 WARNING: Low robustness - strategy may be curve-fitted")
        elif robustness_score > 0.7:
            print("✅ Good robustness - strategy appears stable")
        
        return {
            'status': 'SUCCESS',
            'profitable_count': len(profitable_strategies),
            'total_combinations': len(results_df),
            'success_rate': len(profitable_strategies)/len(results_df),
            'best_strategy': best_strategy.to_dict(),
            'robustness_score': robustness_score
        }
    
    def _analyze_parameter_stability(self, df: pd.DataFrame, param_col: str, metric_col: str) -> float:
        """
        Analyze how stable performance is across parameter values
        Returns correlation coefficient (higher = more stable)
        """
        if len(df) < 3:
            return 0.0
            
        # Group by parameter and calculate mean performance
        param_performance = df.groupby(param_col)[metric_col].mean()
        
        if len(param_performance) < 2:
            return 0.0
            
        # Calculate coefficient of variation (lower = more stable)
        cv = param_performance.std() / param_performance.mean()
        
        # Convert to stability score (0-1, higher = more stable)
        stability_score = max(0, 1 - cv)
        
        return stability_score
    
    def create_heatmaps(self, results_df: pd.DataFrame):
        """
        Create visualization heatmaps
        """
        print("\n📈 Creating performance heatmaps...")
        
        # Create pivot tables for heatmaps
        sharpe_pivot = results_df.pivot_table(
            values='sharpe_ratio',
            index='stop_loss_pct',
            columns='take_profit_pct',
            aggfunc='mean'
        )
        
        return_pivot = results_df.pivot_table(
            values='total_return',
            index='stop_loss_pct', 
            columns='take_profit_pct',
            aggfunc='mean'
        )
        
        # Create plots
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # Sharpe ratio heatmap
        sns.heatmap(sharpe_pivot, annot=True, fmt='.2f', cmap='RdYlGn', 
                   ax=axes[0,0], cbar_kws={'label': 'Sharpe Ratio'})
        axes[0,0].set_title('Sharpe Ratio by Stop Loss vs Take Profit')
        
        # Total return heatmap
        sns.heatmap(return_pivot, annot=True, fmt='.1%', cmap='RdYlGn',
                   ax=axes[0,1], cbar_kws={'label': 'Total Return'})
        axes[0,1].set_title('Total Return by Stop Loss vs Take Profit')
        
        # Win rate distribution
        axes[1,0].hist(results_df['win_rate'], bins=20, alpha=0.7, color='blue')
        axes[1,0].set_xlabel('Win Rate')
        axes[1,0].set_ylabel('Frequency')
        axes[1,0].set_title('Win Rate Distribution')
        axes[1,0].axvline(results_df['win_rate'].mean(), color='red', linestyle='--', 
                         label=f'Mean: {results_df["win_rate"].mean():.1%}')
        axes[1,0].legend()
        
        # Risk/Reward vs Performance scatter
        axes[1,1].scatter(results_df['risk_reward_ratio'], results_df['sharpe_ratio'], 
                         alpha=0.6, c=results_df['total_return'], cmap='RdYlGn')
        axes[1,1].set_xlabel('Risk/Reward Ratio')
        axes[1,1].set_ylabel('Sharpe Ratio')
        axes[1,1].set_title('Risk/Reward vs Sharpe Ratio')
        
        plt.tight_layout()
        plt.savefig('parameter_sensitivity_analysis.png', dpi=300, bbox_inches='tight')
        print("📊 Heatmaps saved as 'parameter_sensitivity_analysis.png'")
        
        return fig

def main():
    """Run parameter sensitivity analysis"""
    analyzer = ParameterSensitivityAnalyzer()
    
    try:
        # Run sensitivity analysis
        results_df = analyzer.run_sensitivity_analysis()
        
        # Analyze results
        analysis = analyzer.analyze_results(results_df)
        
        # Create visualizations
        analyzer.create_heatmaps(results_df)
        
        # Save detailed results
        results_df.to_csv('parameter_sensitivity_results.csv', index=False)
        print(f"\n💾 Detailed results saved to 'parameter_sensitivity_results.csv'")
        
        # Final verdict
        print(f"\n🎯 FINAL VERDICT:")
        if analysis['status'] == 'FAILED':
            print("❌ STRATEGY FAILED - No profitable parameter combinations")
        elif analysis['success_rate'] < 0.2:
            print("🚨 STRATEGY HIGHLY SUSPECT - Very few profitable combinations")
        elif analysis['robustness_score'] < 0.3:
            print("⚠️ STRATEGY MAY BE CURVE-FITTED - Low robustness score")
        else:
            print("✅ STRATEGY SHOWS PROMISE - Multiple profitable combinations with decent robustness")
            
    except Exception as e:
        print(f"❌ Analysis failed: {e}")

if __name__ == "__main__":
    main()
