"""
Unified Redis Manager - Eliminates Redis Code Duplication
=========================================================

This module consolidates all Redis operations across the Project Chimera
microservices, eliminating the duplicate Redis connection and pub/sub code
found in multiple services.

Features:
- Unified Redis connection management
- Standardized pub/sub operations
- Connection pooling and retry logic
- Consistent error handling
- Message publishing utilities
"""

import os
import redis
import json
import logging
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime
from contextlib import contextmanager

from constants import SystemConstants
from error_handling import ChimeraError, retry_with_backoff, log_error_with_context


class RedisConnectionManager:
    """Unified Redis connection manager for all services"""
    
    def __init__(self, redis_url: Optional[str] = None):
        self.redis_url = redis_url or os.environ.get("REDIS_URL")
        if not self.redis_url:
            raise ChimeraError("REDIS_URL environment variable not set")
        
        self._client: Optional[redis.Redis] = None
        self._pubsub: Optional[redis.client.PubSub] = None
        
        logging.info("✅ Redis connection manager initialized")
    
    @property
    def client(self) -> redis.Redis:
        """Get Redis client with lazy initialization"""
        if self._client is None:
            self._client = redis.from_url(
                self.redis_url,
                decode_responses=True,
                socket_connect_timeout=10,
                socket_timeout=10,
                retry_on_timeout=True,
                max_connections=SystemConstants.REDIS_MAX_CONNECTIONS
            )
        return self._client
    
    @contextmanager
    def get_connection(self):
        """Context manager for Redis connections"""
        try:
            yield self.client
        except redis.RedisError as e:
            log_error_with_context(e, context={'redis_operation': True})
            raise ChimeraError(f"Redis operation failed: {str(e)}")
    
    def test_connection(self) -> bool:
        """Test Redis connection"""
        try:
            with self.get_connection() as conn:
                conn.ping()
                return True
        except Exception as e:
            logging.error(f"Redis connection test failed: {e}")
            return False
    
    def close(self):
        """Close Redis connections"""
        if self._pubsub:
            self._pubsub.close()
        if self._client:
            self._client.close()
        logging.info("✅ Redis connections closed")


class UnifiedRedisPublisher:
    """Unified Redis publisher that replaces duplicate publishing code"""
    
    def __init__(self, redis_manager: RedisConnectionManager):
        self.redis = redis_manager
        
        # Channel constants - centralized from all services
        self.CHANNELS = {
            'unlock_events': 'chimera:unlock_events',
            'trade_candidates': 'chimera:trade_candidates',
            'position_opened': 'chimera:position_opened',
            'position_closed': 'chimera:position_closed',
            'close_position': 'chimera:close_position',
            'risk_alerts': 'chimera:risk_alerts',
            'system_events': 'chimera:system_events'
        }
    
    @retry_with_backoff(max_retries=3, base_delay=0.5, exceptions=(redis.RedisError,))
    def publish_message(self, channel: str, message: Dict[str, Any]) -> bool:
        """Publish message to Redis channel with retry logic"""
        try:
            with self.redis.get_connection() as conn:
                # Convert datetime objects to strings for JSON serialization
                serializable_message = self._make_json_serializable(message)
                json_message = json.dumps(serializable_message, default=str)
                
                result = conn.publish(channel, json_message)
                
                logging.debug(f"📤 Published to {channel}: {message.get('type', 'unknown')}")
                return result > 0
                
        except Exception as e:
            log_error_with_context(
                e, 
                context={
                    'channel': channel,
                    'message_type': message.get('type', 'unknown')
                }
            )
            raise ChimeraError(f"Failed to publish message: {str(e)}")
    
    def _make_json_serializable(self, obj: Any) -> Any:
        """Convert objects to JSON-serializable format"""
        if isinstance(obj, dict):
            return {key: self._make_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif hasattr(obj, 'isoformat'):  # datetime objects
            return obj.isoformat()
        elif hasattr(obj, '__dict__'):  # custom objects
            return self._make_json_serializable(obj.__dict__)
        else:
            return obj
    
    # Unified publishing methods that replace duplicate code across services
    
    def publish_unlock_event(self, event: Dict[str, Any]) -> bool:
        """Publish unlock event - replaces duplicate implementations"""
        return self.publish_message(self.CHANNELS['unlock_events'], {
            'type': 'unlock_event',
            'timestamp': datetime.now().isoformat(),
            'data': event
        })
    
    def publish_trade_candidate(self, candidate: Dict[str, Any]) -> bool:
        """Publish trade candidate - replaces duplicate implementations"""
        return self.publish_message(self.CHANNELS['trade_candidates'], {
            'type': 'trade_candidate',
            'timestamp': datetime.now().isoformat(),
            'data': candidate
        })
    
    def publish_position_opened(self, position: Dict[str, Any]) -> bool:
        """Publish position opened event - replaces duplicate implementations"""
        return self.publish_message(self.CHANNELS['position_opened'], {
            'type': 'position_opened',
            'timestamp': datetime.now().isoformat(),
            'data': position
        })
    
    def publish_position_closed(self, position: Dict[str, Any]) -> bool:
        """Publish position closed event - replaces duplicate implementations"""
        return self.publish_message(self.CHANNELS['position_closed'], {
            'type': 'position_closed',
            'timestamp': datetime.now().isoformat(),
            'data': position
        })
    
    def publish_close_position_signal(self, position_data: Dict[str, Any]) -> bool:
        """Publish close position signal - replaces duplicate implementations"""
        return self.publish_message(self.CHANNELS['close_position'], {
            'type': 'close_position_signal',
            'timestamp': datetime.now().isoformat(),
            'data': position_data
        })
    
    def publish_risk_alert(self, alert: Dict[str, Any]) -> bool:
        """Publish risk alert - replaces duplicate implementations"""
        return self.publish_message(self.CHANNELS['risk_alerts'], {
            'type': 'risk_alert',
            'timestamp': datetime.now().isoformat(),
            'data': alert
        })
    
    def publish_system_event(self, event: Dict[str, Any]) -> bool:
        """Publish system event - replaces duplicate implementations"""
        return self.publish_message(self.CHANNELS['system_events'], {
            'type': 'system_event',
            'timestamp': datetime.now().isoformat(),
            'data': event
        })


class UnifiedRedisSubscriber:
    """Unified Redis subscriber that replaces duplicate subscription code"""
    
    def __init__(self, redis_manager: RedisConnectionManager):
        self.redis = redis_manager
        self._subscriptions: Dict[str, Callable] = {}
        self._running = False
    
    def subscribe_to_channel(self, channel: str, callback: Callable[[Dict[str, Any]], None]):
        """Subscribe to a Redis channel with callback"""
        self._subscriptions[channel] = callback
        logging.info(f"✅ Subscribed to channel: {channel}")
    
    def subscribe_to_pattern(self, pattern: str, callback: Callable[[Dict[str, Any]], None]):
        """Subscribe to Redis channel pattern with callback"""
        self._subscriptions[pattern] = callback
        logging.info(f"✅ Subscribed to pattern: {pattern}")
    
    @retry_with_backoff(max_retries=3, base_delay=1.0, exceptions=(redis.RedisError,))
    def start_listening(self):
        """Start listening for messages"""
        if not self._subscriptions:
            raise ChimeraError("No subscriptions configured")
        
        self._running = True
        
        try:
            with self.redis.get_connection() as conn:
                pubsub = conn.pubsub(ignore_subscribe_messages=True)
                
                # Subscribe to all configured channels/patterns
                for channel_or_pattern in self._subscriptions.keys():
                    if '*' in channel_or_pattern:
                        pubsub.psubscribe(channel_or_pattern)
                    else:
                        pubsub.subscribe(channel_or_pattern)
                
                logging.info(f"🎧 Started listening on {len(self._subscriptions)} channels/patterns")
                
                # Message processing loop
                for message in pubsub.listen():
                    if not self._running:
                        break
                    
                    self._handle_message(message)
                    
        except Exception as e:
            log_error_with_context(e, context={'redis_subscription': True})
            raise ChimeraError(f"Redis subscription failed: {str(e)}")
        finally:
            self._running = False
            logging.info("🛑 Redis subscription stopped")
    
    def _handle_message(self, message: Dict[str, Any]):
        """Handle incoming Redis message"""
        if message['type'] not in ['message', 'pmessage']:
            return
        
        try:
            # Parse JSON message
            data = json.loads(message['data'])
            
            # Find appropriate callback
            channel = message.get('channel') or message.get('pattern')
            callback = self._subscriptions.get(channel)
            
            if callback:
                callback(data)
            else:
                logging.warning(f"No callback found for channel: {channel}")
                
        except json.JSONDecodeError as e:
            logging.error(f"Failed to parse message JSON: {e}")
        except Exception as e:
            log_error_with_context(
                e, 
                context={
                    'message_channel': message.get('channel'),
                    'message_type': message.get('type')
                }
            )
    
    def stop_listening(self):
        """Stop listening for messages"""
        self._running = False
        logging.info("🛑 Stopping Redis subscription...")


# Global instances
_redis_manager: Optional[RedisConnectionManager] = None
_redis_publisher: Optional[UnifiedRedisPublisher] = None
_redis_subscriber: Optional[UnifiedRedisSubscriber] = None


def get_redis_manager() -> RedisConnectionManager:
    """Get the global Redis connection manager instance"""
    global _redis_manager
    if _redis_manager is None:
        _redis_manager = RedisConnectionManager()
    return _redis_manager


def get_redis_publisher() -> UnifiedRedisPublisher:
    """Get the global Redis publisher instance"""
    global _redis_publisher
    if _redis_publisher is None:
        redis_manager = get_redis_manager()
        _redis_publisher = UnifiedRedisPublisher(redis_manager)
    return _redis_publisher


def get_redis_subscriber() -> UnifiedRedisSubscriber:
    """Get the global Redis subscriber instance"""
    global _redis_subscriber
    if _redis_subscriber is None:
        redis_manager = get_redis_manager()
        _redis_subscriber = UnifiedRedisSubscriber(redis_manager)
    return _redis_subscriber


def cleanup_redis_connections():
    """Cleanup all Redis connections"""
    global _redis_manager, _redis_publisher, _redis_subscriber
    
    if _redis_subscriber:
        _redis_subscriber.stop_listening()
    
    if _redis_manager:
        _redis_manager.close()
    
    logging.info("✅ Redis cleanup complete")
