import os
import sys
import logging
from typing import Dict, Any, List

# Add common modules to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'common'))

from data_sources import fetch_token_unlocks_data
from db_handler import store_unlock_events, get_upcoming_unlocks
from event_publisher import publish_unlock_event
from unified_config import setup_service_logging

# Setup service-specific logging
logger = setup_service_logging("The Oracle")

def run_oracle_job():
    """
    Main entry point for the Oracle cron job.
    Fetches, stores, and publishes upcoming token unlock events.
    """
    logger.info("🔮 Starting The Oracle daily job...")

    # 1. Fetch data from external sources
    # In a production system, this would involve multiple sources and cross-verification.
    logger.info("📡 Fetching data from TokenUnlocks.com and Vestlab.io APIs...")
    raw_events: List[Dict[str, Any]] = fetch_token_unlocks_data()

    if not raw_events:
        logger.warning("⚠️ No new unlock events were fetched. Exiting job.")
        return

    # 2. Store the raw event data in PostgreSQL for record-keeping and analysis
    logger.info(f"💾 Storing {len(raw_events)} events in the database.")
    store_unlock_events(raw_events)

    # 3. Identify tokens with unlocks in the next 14 days to trigger the workflow
    logging.info("Querying for unlocks within the next 14 days...")
    upcoming_unlocks: List[Dict[str, Any]] = get_upcoming_unlocks(days_ahead=14)
    logging.info(f"Found {len(upcoming_unlocks)} relevant unlocks to publish.")

    # 4. Publish each relevant unlock event to the Redis pub/sub channel
    for unlock in upcoming_unlocks:
        publish_unlock_event(unlock)

    logging.info("The Oracle daily job finished successfully.")

if __name__ == "__main__":
    run_oracle_job()
