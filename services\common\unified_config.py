"""
Unified Configuration Manager - Eliminates Configuration Duplication
====================================================================

This module consolidates all configuration loading and validation across
the Project Chimera microservices, eliminating duplicate environment
variable handling and configuration setup code.

Features:
- Centralized environment variable loading
- Unified configuration validation
- Standardized logging setup
- Type-safe configuration access
- Environment-specific overrides
"""

import os
import logging
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum

from constants import TradingConstants, SystemConstants
from error_handling import ChimeraError


class Environment(Enum):
    """Deployment environment types"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


@dataclass
class UnifiedConfig:
    """Unified configuration that replaces duplicate config loading across services"""
    
    # Environment
    environment: Environment = field(default=Environment.DEVELOPMENT)
    debug: bool = field(default=False)
    
    # Database
    database_url: str = field(default="")
    
    # Redis
    redis_url: str = field(default="")
    
    # Blockchain
    infura_api_key: str = field(default="")
    private_key_path: str = field(default="/etc/secrets/trader-pk")
    
    # Trading Parameters
    paper_trading_mode: bool = field(default=True)
    pressure_score_threshold: float = field(default=0.75)
    stop_loss_pct: float = field(default=0.15)
    take_profit_pct: float = field(default=0.10)
    take_profit_days_before_unlock: int = field(default=1)
    borrow_amount_per_trade: float = field(default=1000.0)
    monitoring_interval_seconds: int = field(default=60)
    
    # API Keys
    coingecko_api_key: str = field(default="")
    dexscreener_api_key: str = field(default="")
    dextools_api_key: str = field(default="")
    etherscan_api_key: str = field(default="")
    
    # Notifications
    telegram_bot_token: str = field(default="")
    telegram_chat_id: str = field(default="")
    
    # Logging
    log_level: str = field(default="INFO")
    log_format: str = field(default="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    
    def __post_init__(self):
        """Load and validate configuration from environment variables"""
        self._load_from_environment()
        self._validate_configuration()
        self._setup_logging()
    
    def _load_from_environment(self):
        """Load configuration from environment variables"""
        # Environment
        env_str = os.getenv("ENVIRONMENT", "development").lower()
        try:
            self.environment = Environment(env_str)
        except ValueError:
            self.environment = Environment.DEVELOPMENT
        
        self.debug = os.getenv("DEBUG", "false").lower() == "true"
        
        # Database & Redis
        self.database_url = os.getenv("DATABASE_URL", "")
        self.redis_url = os.getenv("REDIS_URL", "")
        
        # Blockchain
        self.infura_api_key = os.getenv("INFURA_API_KEY", "")
        self.private_key_path = os.getenv("PRIVATE_KEY_PATH", "/etc/secrets/trader-pk")
        
        # Trading Parameters
        self.paper_trading_mode = os.getenv("PAPER_TRADING_MODE", "true").lower() == "true"
        self.pressure_score_threshold = float(os.getenv("PRESSURE_SCORE_THRESHOLD", "0.75"))
        self.stop_loss_pct = float(os.getenv("STOP_LOSS_PCT", "0.15"))
        self.take_profit_pct = float(os.getenv("TAKE_PROFIT_PCT", "0.10"))
        self.take_profit_days_before_unlock = int(os.getenv("TAKE_PROFIT_DAYS_BEFORE_UNLOCK", "1"))
        self.borrow_amount_per_trade = float(os.getenv("BORROW_AMOUNT_PER_TRADE", "1000"))
        self.monitoring_interval_seconds = int(os.getenv("MONITORING_INTERVAL_SECONDS", "60"))
        
        # API Keys
        self.coingecko_api_key = os.getenv("COINGECKO_API_KEY", "")
        self.dexscreener_api_key = os.getenv("DEXSCREENER_API_KEY", "")
        self.dextools_api_key = os.getenv("DEXTOOLS_API_KEY", "")
        self.etherscan_api_key = os.getenv("ETHERSCAN_API_KEY", "")
        
        # Notifications
        self.telegram_bot_token = os.getenv("TELEGRAM_BOT_TOKEN", "")
        self.telegram_chat_id = os.getenv("TELEGRAM_CHAT_ID", "")
        
        # Logging
        self.log_level = os.getenv("LOG_LEVEL", "INFO").upper()
        self.log_format = os.getenv("LOG_FORMAT", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    
    def _validate_configuration(self):
        """Validate configuration values"""
        # Validate log level
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.log_level not in valid_levels:
            raise ChimeraError(f"Invalid log level: {self.log_level}")
        
        # Validate trading parameters
        if not 0 < self.stop_loss_pct < 1:
            raise ChimeraError("STOP_LOSS_PCT must be between 0 and 1")
        
        if not 0 < self.take_profit_pct < 1:
            raise ChimeraError("TAKE_PROFIT_PCT must be between 0 and 1")
        
        if self.pressure_score_threshold < 0:
            raise ChimeraError("PRESSURE_SCORE_THRESHOLD must be positive")
        
        if self.borrow_amount_per_trade <= 0:
            raise ChimeraError("BORROW_AMOUNT_PER_TRADE must be positive")
        
        # Production-specific validations
        if self.environment == Environment.PRODUCTION:
            if not self.database_url:
                raise ChimeraError("DATABASE_URL is required for production")
            
            if not self.redis_url:
                raise ChimeraError("REDIS_URL is required for production")
            
            if self.debug:
                logging.warning("Debug mode enabled in production environment")
            
            if self.paper_trading_mode:
                logging.warning("Paper trading mode enabled in production environment")
    
    def _setup_logging(self):
        """Setup logging based on configuration"""
        logging.basicConfig(
            level=getattr(logging, self.log_level),
            format=self.log_format,
            force=True  # Override any existing logging configuration
        )
        
        # Log configuration summary
        logging.info(f"✅ Configuration loaded for {self.environment.value} environment")
        logging.info(f"📊 Trading: paper_mode={self.paper_trading_mode}, "
                    f"stop_loss={self.stop_loss_pct*100:.1f}%, "
                    f"take_profit={self.take_profit_pct*100:.1f}%")
    
    def get_masked_config(self) -> Dict[str, Any]:
        """Get configuration with sensitive values masked"""
        config_dict = {}
        
        for field_name, field_value in self.__dict__.items():
            # Mask sensitive values
            if any(sensitive in field_name.lower() for sensitive in ['key', 'token', 'password', 'secret', 'url']):
                if field_value:
                    config_dict[field_name] = field_value[:8] + "..." if len(str(field_value)) > 8 else "***"
                else:
                    config_dict[field_name] = None
            else:
                config_dict[field_name] = field_value
        
        return config_dict
    
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.environment == Environment.PRODUCTION
    
    def is_development(self) -> bool:
        """Check if running in development environment"""
        return self.environment == Environment.DEVELOPMENT
    
    def has_database_config(self) -> bool:
        """Check if database configuration is available"""
        return bool(self.database_url)
    
    def has_redis_config(self) -> bool:
        """Check if Redis configuration is available"""
        return bool(self.redis_url)
    
    def has_blockchain_config(self) -> bool:
        """Check if blockchain configuration is available"""
        return bool(self.infura_api_key)
    
    def has_telegram_config(self) -> bool:
        """Check if Telegram configuration is available"""
        return bool(self.telegram_bot_token and self.telegram_chat_id)


class ConfigurationManager:
    """Configuration manager that provides unified access to configuration"""
    
    def __init__(self):
        self._config: Optional[UnifiedConfig] = None
    
    @property
    def config(self) -> UnifiedConfig:
        """Get the configuration instance"""
        if self._config is None:
            self._config = UnifiedConfig()
        return self._config
    
    def reload_config(self) -> UnifiedConfig:
        """Reload configuration from environment variables"""
        self._config = UnifiedConfig()
        return self._config
    
    def validate_service_requirements(self, service_name: str, required_configs: list) -> bool:
        """Validate that required configurations are available for a service"""
        missing_configs = []
        
        for requirement in required_configs:
            if requirement == 'database' and not self.config.has_database_config():
                missing_configs.append('DATABASE_URL')
            elif requirement == 'redis' and not self.config.has_redis_config():
                missing_configs.append('REDIS_URL')
            elif requirement == 'blockchain' and not self.config.has_blockchain_config():
                missing_configs.append('INFURA_API_KEY')
            elif requirement == 'telegram' and not self.config.has_telegram_config():
                missing_configs.append('TELEGRAM_BOT_TOKEN/TELEGRAM_CHAT_ID')
        
        if missing_configs:
            logging.error(f"❌ {service_name} missing required configuration: {', '.join(missing_configs)}")
            return False
        
        logging.info(f"✅ {service_name} configuration validation passed")
        return True


# Global configuration manager instance
_config_manager: Optional[ConfigurationManager] = None


def get_config_manager() -> ConfigurationManager:
    """Get the global configuration manager instance"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigurationManager()
    return _config_manager


def get_config() -> UnifiedConfig:
    """Get the global configuration instance"""
    return get_config_manager().config


# Convenience functions that replace duplicate environment variable access
def get_database_url() -> str:
    """Get database URL - replaces os.environ.get("DATABASE_URL")"""
    return get_config().database_url


def get_redis_url() -> str:
    """Get Redis URL - replaces os.environ.get("REDIS_URL")"""
    return get_config().redis_url


def get_pressure_score_threshold() -> float:
    """Get pressure score threshold - replaces duplicate constant definitions"""
    return get_config().pressure_score_threshold


def get_stop_loss_pct() -> float:
    """Get stop loss percentage - replaces duplicate constant definitions"""
    return get_config().stop_loss_pct


def get_take_profit_pct() -> float:
    """Get take profit percentage - replaces duplicate constant definitions"""
    return get_config().take_profit_pct


def is_paper_trading_mode() -> bool:
    """Check if paper trading mode is enabled - replaces duplicate checks"""
    return get_config().paper_trading_mode


def setup_service_logging(service_name: str):
    """Setup logging for a service - replaces duplicate logging setup"""
    config = get_config()
    
    # Service-specific logger
    logger = logging.getLogger(service_name)
    logger.setLevel(getattr(logging, config.log_level))
    
    # Add service name to log format if not already present
    if service_name not in config.log_format:
        formatter = logging.Formatter(f"%(asctime)s - {service_name} - %(levelname)s - %(message)s")
        
        # Update all handlers
        for handler in logging.getLogger().handlers:
            handler.setFormatter(formatter)
    
    logging.info(f"✅ Logging setup complete for {service_name}")
    return logger
